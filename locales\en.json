{"auth": {"error": {"invalidLoginInput": "Oops! It looks like a field is empty. Please check your details and try again.", "loginValidationFailed": "Hmm, that email or password doesn't look right. Want to give it another try?", "invalidRegisterInput": "Oops! It looks like a field is empty. Please fill in all the details to continue.", "failedToParseFormData": "Uh-oh! We had a little trouble reading your info. Could you please try submitting it again?", "invalidOboardingAgeRange": "Hmm, something seems off with the age range. Please double-check and try again.", "invalidOboardingFinancialSituation": "Oops, something about your financial situation seems incorrect. Could you please check it?", "invalidOboardingFinancialGoal": "Hmm, we didn't quite get your financial goal. How about reviewing and trying again?", "invalidOboardingPersonalInterest": "It seems your interests weren't saved. Let's give it one more try!", "failedToProcessPhoto": "We couldn't upload your photo. Maybe try a different one or give it another go in a moment?", "invalidFileType": "That file type isn't supported. Please use a JPG, JPEG, or PNG image!", "fileTooLarge": "Whoa, that's a big photo! Please try one that's 5MB or smaller.", "invalidRefreshTokenInput": "Oops, something unexpected happened on our end. Please try that again.", "invalidForgotPasswordInput": "Hmm, something went wrong. Check your details and try requesting a recovery link again.", "invalidResetPasswordInput": "Oops! Something went wrong while resetting your password. Let's try one more time.", "invalidCheckPasswordInput": "That password doesn't look quite right. Please take a look and try again.", "userNotLoggedIn": "It looks like you're not logged in. Please sign in to continue your adventure!", "invalidAdminLoginInput": "Hmm, the admin login details don't seem right. Please check them and try again.", "": "", "failedToUploadPhoto": "Oops, we couldn't upload your photo. How about trying with a different image?", "failedToCreateToken": "Oops, something went wrong on our end. Please try again in a moment.", "failedToRetrieveUserAfterCreation": "Your account was created, but we couldn't connect you right now. How about trying to log in?", "brevoNotifierNotAvailable": "Oops! We couldn't send the email right now. Please try again in a few minutes.", "userNotAdmin": "Oops! It looks like this is a restricted area. Just for the game masters!", "userNotHR": "Oops! It looks like this is a restricted area. Just for the HR!", "invalidHRLoginInput": "Hmm, the HR login details don't seem right. Please check them and try again."}}, "financialsheet": {"error": {"conflict": "You already have a financial sheet! Keep organizing your finances.", "createFailed": "Oops! We couldn't create your financial sheet. Please try again.", "findFailed": "Oops! We couldn't load your financial sheet. Please try again.", "findAllFailed": "Oops! We couldn't load the financial sheets. Please try again.", "notFound": "Financial sheet not found. How about creating a new one?", "invalidId": "Invalid financial sheet ID. Please check your data and try again.", "userNotFound": "Financial sheet not found for this user. Start by creating one!", "findByUserFailed": "Oops! We couldn't load your financial sheet. Please try again.", "findByUsersFailed": "Oops! We couldn't load the financial sheets. Please try again.", "conflictUpdate": "Couldn't update your financial sheet. Please try again.", "updateFailed": "Oops! We couldn't save your changes. Please try again.", "deleteFailed": "Oops! We couldn't delete your financial sheet. Please try again.", "invalidMonth": "Oops! The month must be between 1 and 12. Please check and try again.", "duplicateMonth": "Whoops! You've already chosen this month. Select different months for recurrence.", "sameMonthAsOriginal": "Can't repeat in the same month as the original transaction. Choose other months!", "recordAlreadyExists": "You already have a financial sheet! Keep organizing your finances.", "invalidRecord": "Oops! Invalid data. Please check your information and try again.", "noTransactionsAlreadyMarked": "You've already marked 'no transactions' for today! Continue your streak tomorrow.", "noTransactionsAlreadyMarkedDate": "You've already marked 'no transactions' for this date. Choose another day!", "cannotMarkSameDayAsTransaction": "Can't mark 'no transactions' on the same day as a real transaction.", "invalidFinancialSheetId": "Invalid financial sheet ID. Please check the data and try again.", "invalidMonthParameter": "Invalid month! Must be between 1 and 12. Please check and try again.", "invalidYearParameter": "Invalid year! Please check the format and try again.", "invalidInput": "Oops! Invalid data. Please check the information and try again.", "validationFailed": "Some fields aren't filled correctly. Please take a look!", "dreamTransactionInput": "Oops! We couldn't process your dream data. Please try again.", "dreamTransactionValidation": "Some of your dream data is incorrect. Please check and try again.", "invalidTransactionId": "Invalid transaction ID. Please check the data and try again.", "invalidMoneySource": "Oops! Invalid money source. Please choose a valid option and try again.", "invalidPaymentMethod": "Oops! Invalid payment method. Please select a valid payment option.", "invalidCategoryIdentifier": "Invalid category identifier. Please check the data and try again.", "invalidCategoryType": "Invalid category type. Please choose a valid type and try again.", "invalidCategoryBackground": "Invalid category background color. Please select a valid color.", "invalidFinancialRecordId": "Oops! Invalid financial record ID. Please check the data and try again.", "invalidCategoryId": "Oops! Invalid category ID. Please check the data and try again.", "cannotDeleteSystemCategories": "Whoops! You can't delete system categories. They're essential for the app to work!", "canOnlyDeleteOwnCategories": "You can only delete your own categories. This one isn't yours!", "categoryInUse": "This category is being used in transactions! Remove the transactions first.", "transactionTypeMismatch": "Oops! The transaction type doesn't match the category type. Please check and try again.", "invalidTransaction": "Oops! Invalid transaction data. Please check the information and try again.", "invalidDreamId": "Oops! Invalid dream ID. Please check the data and try again.", "transactionNotFound": "Transaction not found. It may have been removed or doesn't exist.", "cannotMarkBeforeYesterday": "Can't mark 'no transactions' for dates before yesterday. Stay focused on the present!"}, "category": {"error": {"conflict": "This category already exists! Choose a different name.", "createFailed": "Oops! We couldn't create your category. Please try again.", "findFailed": "Oops! We couldn't load your categories. Please try again.", "notFound": "Category not found. How about creating a new one?", "invalidId": "Invalid category ID. Please check your data and try again.", "conflictUpdate": "Couldn't update your category. Please try again.", "updateFailed": "Oops! We couldn't save the category changes. Please try again.", "deleteFailed": "Oops! We couldn't delete your category. Please try again.", "findByIdFailed": "Oops! We couldn't find this category. Please try again.", "findByNameFailed": "Oops! We couldn't find the category by name. Please try again."}}}, "progression": {"error": {"conflict": "You already have saved progress! Continue where you left off.", "createFailed": "Oops! We couldn't save your progress. Please try again.", "invalidIdFormat": "Invalid progress ID. Please check the data and try again.", "notFound": "Progress not found. How about starting a new journey?", "findFailed": "Oops! We couldn't load your progress. Please try again.", "notFoundForUser": "No progress found. Start your learning journey now!", "findByUserFailed": "Oops! We couldn't load your progress. Please try again.", "updateConflict": "Unable to update your progress. Please try again.", "updateFailed": "Oops! We couldn't save your achievements. Please try again.", "notFoundForUpdate": "Progress not found for update. Please try again.", "deleteFailed": "Oops! We couldn't reset your progress. Please try again.", "notFoundForDeletion": "Progress not found for deletion. It may have already been removed.", "findTrailProgressionsFailed": "Oops! We couldn't load your trails. Please try again."}}, "user": {"error": {"hashPassword": "Oops! We had a little trouble saving your password. How about trying again?", "forbidden": "Whoops! It looks like you need a special key to access this.", "invalidCredentials": "Hmm, that email and password combo doesn't look right. Let's give it another try!", "resetPassword": "Oops! Something went wrong while trying to reset your password. Please give it one more try.", "mergeFailed": "Uh-oh, we had a problem updating your information. Could you please try again?", "processPassword": "Oops, we had a little trouble with your password. Could you please try again?", "emailRequired": "Oops, the email is missing! Please fill it in to continue.", "invalidEmail": "Hmm, that email doesn't look quite right. How about taking a look?", "emptyId": "Oops! Something unexpected happened on our end. Please try again.", "nameRequired": "Almost there! We just need your name to continue.", "passwordRequired": "Don't forget your password! It's super important for keeping your account safe.", "referralCodeRequired": "The referral code is missing! Please fill it in to continue.", "setRoleNotAllowed": "Whoops! That's a super-powerful action that can't be done from here.", "phoneRequired": "Oops, we need your phone number to continue.", "passwordRequirements": "For a super-secure password, it needs:\n- At least 6 characters\n- One uppercase letter (A-Z)\n- One lowercase letter (a-z)\n- One number (0-9)\n- One special character (!@#$)", "": "", "conflict": "This user already exists. Try with a different email!", "notFoundById": "User not found. Please check if the ID is correct.", "notFoundByEmail": "No user found with this email. How about creating an account?", "notFoundByReferral": "Invalid referral code. Please check if you typed it correctly.", "deletedNotFoundByEmail": "No deleted account found with this email.", "conflictUpdate": "Unable to update. This email is already being used by another user.", "notFoundForUpdate": "User not found for update. Please try again.", "notFoundForDeletion": "User not found for deletion. It may have already been removed.", "createFailed": "Oops! Something went wrong while creating your account. Please try again.", "deletedCreateFailed": "Oops! Something went wrong. Please try again later.", "findByIdFailed": "Oops! Something went wrong while searching for the user. Please try again.", "findAllFailed": "Oops! Something went wrong while loading users. Please try again.", "decodeUserFailed": "Oops! Something went wrong. Please try again later.", "adminUsersNotFound": "Oops! Something went wrong while searching for administrators. Please try again.", "accessDenied": "Oops! Access denied. You don't have permission to perform this action.", "decodeAdminUserFailed": "Oops! Something went wrong. Please try again later.", "findByEmailFailed": "Oops! Something went wrong while searching for the user. Please try again.", "findByReferralFailed": "Oops! Something went wrong while verifying the code. Please try again.", "findByReferringUserIdFailed": "Oops! Something went wrong. Please try again later.", "cursorError": "Oops! Something went wrong. Please try again later.", "findWithFilterFailed": "Oops! Something went wrong while searching for users. Please try again.", "deletedFindByEmailFailed": "Oops! Something went wrong. Please try again later.", "invalidId": "Invalid user ID. Please check the data and try again.", "updateFailed": "Oops! Something went wrong while updating. Please try again.", "deleteFailed": "Oops! Something went wrong while deleting. Please try again.", "deletedConflictExists": "This account has already been deleted previously."}}}